import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { 
  GenerateRequirementsUseCase,
  GenerateDesignFromRequirementsUseCase,
  GenerateCodeFromDesignUseCase
} from '../../application/use-cases/index.js';
import { McpPromptAdapter } from '../../infrastructure/mcp/McpPromptAdapter.js';

/**
 * Factory pour créer et configurer le serveur MCP avec l'architecture hexagonale.
 * Remplace la fonction createServer originale en maintenant la même signature publique.
 */
export class McpServerFactory {
  /**
   * Crée et configure un serveur MCP avec tous les cas d'utilisation et adaptateurs
   * @returns Le serveur MCP configuré
   */
  static createServer(): McpServer {
    const server = new McpServer({
      name: "Spec-Driven Development MCP Server",
      version: "0.1.0",
    });

    // Instanciation des adaptateurs d'infrastructure
    const promptAdapter = new McpPromptAdapter();

    // Instanciation des cas d'utilisation avec injection de dépendance
    const generateRequirementsUseCase = new GenerateRequirementsUseCase(promptAdapter);
    const generateDesignUseCase = new GenerateDesignFromRequirementsUseCase(promptAdapter);
    const generateCodeUseCase = new GenerateCodeFromDesignUseCase(promptAdapter);

    // Enregistrement du prompt generate-requirements
    server.registerPrompt(
      "generate-requirements",
      {
        title: "Generate Requirements Document",
        description: "Generate requirements.md using EARS format",
        argsSchema: { 
          requirements: z.string().describe("High-level requirements of the application. Example: 'A Vue.js todo application with task creation, completion tracking, and local storage persistence'")
        }
      },
      ({ requirements }) => generateRequirementsUseCase.execute(requirements)
    );

    // Enregistrement du prompt generate-design-from-requirements
    server.registerPrompt(
      "generate-design-from-requirements",
      {
        title: "Generate Design Document from Requirements Document",
        description: "Generate design.md from requirements.md",
      },
      () => generateDesignUseCase.execute()
    );

    // Enregistrement du prompt generate-code-from-design
    server.registerPrompt(
      "generate-code-from-design",
      {
        title: "Generate Code from Design Document",
        description: "Generate code from design.md"
      },
      () => generateCodeUseCase.execute()
    );

    return server;
  }
}
